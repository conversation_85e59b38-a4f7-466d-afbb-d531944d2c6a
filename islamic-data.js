// 🕌 البيانات الإسلامية للمسبحة الإلكترونية الفريدة

const ISLAMIC_DATA = {
  // الأذكار والتسبيحات
  adhkar: [
    {
      arabic: "سُبْحَانَ اللَّهِ",
      translation: "Glory be to Allah",
      transliteration: "<PERSON><PERSON> Allah",
      meaning: "تنزيه الله عن كل نقص",
      reward: "من قالها مائة مرة غُفرت له ذنوبه",
      count: 33,
    },
    {
      arabic: "الْحَمْدُ لِلَّهِ",
      translation: "Praise be to <PERSON>",
      transliteration: "Alham<PERSON>li<PERSON>",
      meaning: "الثناء والشكر لله",
      reward: "تملأ الميزان يوم القيامة",
      count: 33,
    },
    {
      arabic: "اللَّهُ أَكْبَرُ",
      translation: "Allah is the Greatest",
      transliteration: "Allahu Akbar",
      meaning: "الله أعظم من كل شيء",
      reward: "أحب الكلام إلى الله",
      count: 34,
    },
    {
      arabic: "لَا إِلَٰهَ إِلَّا اللَّهُ",
      translation: "There is no god but Allah",
      transliteration: "La ilaha illa Allah",
      meaning: "شهادة التوحيد",
      reward: "أفضل الذكر",
      count: 100,
    },
    {
      arabic: "لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ",
      translation: "There is no power except with Allah",
      transliteration: "La hawla wa la quwwata illa billah",
      meaning: "لا قوة إلا بالله",
      reward: "كنز من كنوز الجنة",
      count: 100,
    },
    {
      arabic: "أَسْتَغْفِرُ اللَّهَ",
      translation: "I seek forgiveness from Allah",
      transliteration: "Astaghfirullah",
      meaning: "طلب المغفرة من الله",
      reward: "يفتح أبواب الرزق",
      count: 100,
    },
    {
      arabic: "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ",
      translation: "Glory be to Allah and praise Him",
      transliteration: "Subhan Allahi wa bihamdihi",
      meaning: "تسبيح وحمد لله",
      reward: "غُرست له نخلة في الجنة",
      count: 100,
    },
    {
      arabic: "سُبْحَانَ اللَّهِ الْعَظِيمِ",
      translation: "Glory be to Allah, the Magnificent",
      transliteration: "Subhan Allahi al-Azeem",
      meaning: "تسبيح الله العظيم",
      reward: "ثقيلة في الميزان",
      count: 100,
    },
    {
      arabic: "رَبِّ اغْفِرْ لِي",
      translation: "My Lord, forgive me",
      transliteration: "Rabbi ghfir li",
      meaning: "دعاء طلب المغفرة",
      reward: "دعاء مستجاب",
      count: 100,
    },
    {
      arabic: "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
      translation: "In the name of Allah, the Most Gracious, the Most Merciful",
      transliteration: "Bismillahi ar-Rahman ar-Raheem",
      meaning: "البسملة الشريفة",
      reward: "بركة في كل عمل",
      count: 100,
    },
  ],

  // أذكار الصباح والمساء
  morningAdhkar: [
    {
      arabic: "أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ",
      translation:
        "We have reached the morning and with it Allah's sovereignty",
      times: 1,
    },
    {
      arabic: "اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَٰهَ إِلَّا أَنْتَ",
      translation: "O Allah, You are my Lord, there is no god but You",
      times: 1,
    },
    {
      arabic: "أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ",
      translation: "I seek refuge in Allah from Satan the accursed",
      times: 3,
    },
  ],

  eveningAdhkar: [
    {
      arabic: "أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ",
      translation:
        "We have reached the evening and with it Allah's sovereignty",
      times: 1,
    },
    {
      arabic: "اللَّهُمَّ بِكَ أَمْسَيْنَا وَبِكَ أَصْبَحْنَا",
      translation:
        "O Allah, by You we have reached the evening and by You we reach the morning",
      times: 1,
    },
  ],

  // أسماء الله الحسنى
  asmaAlHusna: [
    { arabic: "الرَّحْمَٰنُ", translation: "The Most Gracious" },
    { arabic: "الرَّحِيمُ", translation: "The Most Merciful" },
    { arabic: "الْمَلِكُ", translation: "The King" },
    { arabic: "الْقُدُّوسُ", translation: "The Holy" },
    { arabic: "السَّلَامُ", translation: "The Peace" },
    { arabic: "الْمُؤْمِنُ", translation: "The Believer" },
    { arabic: "الْمُهَيْمِنُ", translation: "The Guardian" },
    { arabic: "الْعَزِيزُ", translation: "The Mighty" },
    { arabic: "الْجَبَّارُ", translation: "The Compeller" },
    { arabic: "الْمُتَكَبِّرُ", translation: "The Supreme" },
    // يمكن إضافة باقي الأسماء...
  ],

  // آيات قرآنية مختارة
  quranVerses: [
    {
      arabic: "وَاذْكُرُوا اللَّهَ كَثِيرًا لَّعَلَّكُمْ تُفْلِحُونَ",
      translation: "And remember Allah much that you may succeed",
      surah: "الجمعة",
      ayah: 10,
    },
    {
      arabic: "فَاذْكُرُونِي أَذْكُرْكُمْ وَاشْكُرُوا لِي وَلَا تَكْفُرُونِ",
      translation: "So remember Me; I will remember you",
      surah: "البقرة",
      ayah: 152,
    },
    {
      arabic: "وَلَذِكْرُ اللَّهِ أَكْبَرُ",
      translation: "And the remembrance of Allah is greater",
      surah: "العنكبوت",
      ayah: 45,
    },
  ],

  // مواقيت الصلاة (القاهرة كمثال)
  prayerTimes: {
    city: "القاهرة",
    country: "مصر",
    times: {
      fajr: "04:30",
      sunrise: "06:00",
      dhuhr: "12:15",
      asr: "15:45",
      maghrib: "18:30",
      isha: "20:00",
    },
    qibla: "136° جنوب شرق",
  },

  // الإذاعات القرآنية
  radioStations: [
    {
      name: "إذاعة القرآن الكريم - السعودية",
      url: "https://qurango.net/radio/tafseer",
      country: "السعودية",
      type: "قرآن وتفسير",
    },
    {
      name: "إذاعة القرآن الكريم - مصر",
      url: "https://qurango.net/radio/quran",
      country: "مصر",
      type: "تلاوات متنوعة",
    },
    {
      name: "الشيخ عبد الباسط عبد الصمد",
      url: "https://qurango.net/radio/abdulbasit",
      reciter: "عبد الباسط عبد الصمد",
      type: "تلاوات مرتلة",
    },
    {
      name: "الشيخ محمد صديق المنشاوي",
      url: "https://qurango.net/radio/minshawi",
      reciter: "محمد صديق المنشاوي",
      type: "تلاوات مجودة",
    },
  ],

  // الأدعية المأثورة
  supplications: [
    {
      arabic:
        "رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ",
      translation:
        "Our Lord, give us good in this world and good in the next world, and save us from the punishment of the Fire",
      occasion: "دعاء شامل",
    },
    {
      arabic: "رَبِّ اشْرَحْ لِي صَدْرِي وَيَسِّرْ لِي أَمْرِي",
      translation: "My Lord, expand for me my breast and ease for me my task",
      occasion: "عند الصعوبات",
    },
    {
      arabic: "حَسْبُنَا اللَّهُ وَنِعْمَ الْوَكِيلُ",
      translation:
        "Sufficient for us is Allah, and He is the best Disposer of affairs",
      occasion: "عند الخوف والقلق",
    },
  ],

  // الأهداف والإنجازات
  achievements: [
    {
      id: "first_hundred",
      name: "المئة الأولى",
      description: "أكمل 100 تسبيحة",
      icon: "🥉",
      target: 100,
    },
    {
      id: "first_thousand",
      name: "الألف الأولى",
      description: "أكمل 1000 تسبيحة",
      icon: "🥈",
      target: 1000,
    },
    {
      id: "ten_thousand",
      name: "عشرة آلاف",
      description: "أكمل 10000 تسبيحة",
      icon: "🥇",
      target: 10000,
    },
    {
      id: "daily_streak_7",
      name: "أسبوع متواصل",
      description: "سبح لمدة 7 أيام متتالية",
      icon: "🔥",
      target: 7,
    },
    {
      id: "daily_streak_30",
      name: "شهر متواصل",
      description: "سبح لمدة 30 يوم متتالية",
      icon: "⭐",
      target: 30,
    },
  ],

  // الألوان والثيمات
  themes: {
    light: {
      name: "النهار",
      primary: "#2d5a27",
      secondary: "#d4af37",
      background: "#f8f9fa",
      text: "#8b4513",
    },
    dark: {
      name: "الليل",
      primary: "#1e3a8a",
      secondary: "#fbbf24",
      background: "#1f2937",
      text: "#f9fafb",
    },
    mosque: {
      name: "المسجد",
      primary: "#059669",
      secondary: "#d97706",
      background: "#ecfdf5",
      text: "#064e3b",
    },
  },

  // الإعدادات الافتراضية
  defaultSettings: {
    targetCounts: [33, 99, 100, 500, 1000],
    soundEnabled: true,
    vibrationEnabled: true,
    autoReset: true,
    showTranslation: true,
    fontSize: "medium",
    theme: "light",
    language: "ar",
  },

  // رسائل التحفيز
  motivationalMessages: [
    "بارك الله فيك! استمر في الذكر",
    "ذكر الله طمأنينة للقلوب",
    "كل تسبيحة نور في ميزانك",
    "الذاكرون الله كثيراً والذاكرات",
    "اذكروا الله كثيراً لعلكم تفلحون",
    "ولذكر الله أكبر",
    "فاذكروني أذكركم",
    "الذكر جلاء القلوب",
  ],

  // أوقات مستحبة للذكر
  recommendedTimes: [
    {
      time: "بعد الفجر",
      description: "من أفضل أوقات الذكر",
      dhikr: "سبحان الله وبحمده",
    },
    {
      time: "بعد المغرب",
      description: "وقت إجابة الدعاء",
      dhikr: "لا إله إلا الله",
    },
    {
      time: "قبل النوم",
      description: "ختام اليوم بالذكر",
      dhikr: "أستغفر الله",
    },
  ],
};

// دوال مساعدة للبيانات الإسلامية
const IslamicDataHelpers = {
  // الحصول على ذكر عشوائي
  getRandomDhikr() {
    const randomIndex = Math.floor(Math.random() * ISLAMIC_DATA.adhkar.length);
    return ISLAMIC_DATA.adhkar[randomIndex];
  },

  // الحصول على آية عشوائية
  getRandomVerse() {
    const randomIndex = Math.floor(
      Math.random() * ISLAMIC_DATA.quranVerses.length
    );
    return ISLAMIC_DATA.quranVerses[randomIndex];
  },

  // الحصول على رسالة تحفيزية عشوائية
  getRandomMotivation() {
    const randomIndex = Math.floor(
      Math.random() * ISLAMIC_DATA.motivationalMessages.length
    );
    return ISLAMIC_DATA.motivationalMessages[randomIndex];
  },

  // فحص الإنجازات
  checkAchievements(totalCount, streakDays) {
    const unlockedAchievements = [];

    ISLAMIC_DATA.achievements.forEach((achievement) => {
      if (
        achievement.id.includes("thousand") &&
        totalCount >= achievement.target
      ) {
        unlockedAchievements.push(achievement);
      } else if (
        achievement.id.includes("streak") &&
        streakDays >= achievement.target
      ) {
        unlockedAchievements.push(achievement);
      }
    });

    return unlockedAchievements;
  },

  // تحويل الوقت إلى 12 ساعة
  formatTime12Hour(time24) {
    const [hours, minutes] = time24.split(":");
    const hour12 = hours % 12 || 12;
    const ampm = hours < 12 ? "ص" : "م";
    return `${hour12}:${minutes} ${ampm}`;
  },

  // حساب الوقت المتبقي للصلاة القادمة
  getNextPrayerTime() {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const prayers = [
      { name: "الفجر", time: "04:30" },
      { name: "الشروق", time: "06:00" },
      { name: "الظهر", time: "12:15" },
      { name: "العصر", time: "15:45" },
      { name: "المغرب", time: "18:30" },
      { name: "العشاء", time: "20:00" },
    ];

    for (let prayer of prayers) {
      const [hours, minutes] = prayer.time.split(":");
      const prayerTime = parseInt(hours) * 60 + parseInt(minutes);

      if (currentTime < prayerTime) {
        const remainingMinutes = prayerTime - currentTime;
        const remainingHours = Math.floor(remainingMinutes / 60);
        const remainingMins = remainingMinutes % 60;

        return {
          name: prayer.name,
          time: prayer.time,
          remaining: `${remainingHours}:${remainingMins
            .toString()
            .padStart(2, "0")}`,
        };
      }
    }

    // إذا انتهت جميع الصلوات، فالصلاة القادمة هي فجر اليوم التالي
    return {
      name: "الفجر",
      time: "04:30",
      remaining: "غداً",
    };
  },

  // استيراد الدالة الجديدة بدلاً من الدالة القديمة
  // استبدل الدالة القديمة getAccurateHijriDate
  getAccurateHijriDate() {
    // استخدم الدالة من hijri-date.js
    if (typeof window.getAccurateHijriDate === "function") {
      return window.getAccurateHijriDate(new Date());
    }
    // fallback
    return { day: 1, month: "محرم", year: 1440, weekday: "", formatted: "١ محرم ١٤٤٠ هـ" };
  },

  // تحديث التاريخ الهجري بناءً على التاريخ الميلادي
  calculateHijriFromGregorian(gregorianDate) {
    // خوارزمية تحويل مبسطة (يمكن تحسينها لاحقاً)
    const baseGregorian = new Date("2024-06-16"); // تاريخ مرجعي
    const baseHijri = { year: 1446, month: 11, day: 7 }; // 7 ذو الحجة 1446

    const diffDays = Math.floor(
      (gregorianDate - baseGregorian) / (1000 * 60 * 60 * 24)
    );

    // حساب تقريبي للتاريخ الهجري
    let hijriDay = baseHijri.day + diffDays;
    let hijriMonth = baseHijri.month;
    let hijriYear = baseHijri.year;

    // تعديل الشهر والسنة حسب الحاجة (تقريبي)
    while (hijriDay > 29) {
      hijriDay -= 29;
      hijriMonth++;
      if (hijriMonth > 11) {
        hijriMonth = 0;
        hijriYear++;
      }
    }

    while (hijriDay < 1) {
      hijriDay += 29;
      hijriMonth--;
      if (hijriMonth < 0) {
        hijriMonth = 11;
        hijriYear--;
      }
    }

    const hijriMonths = [
      "محرم",
      "صفر",
      "ربيع الأول",
      "ربيع الثاني",
      "جمادى الأولى",
      "جمادى الثانية",
      "رجب",
      "شعبان",
      "رمضان",
      "شوال",
      "ذو القعدة",
      "ذو الحجة",
    ];

    return {
      day: hijriDay,
      month: hijriMonths[hijriMonth],
      year: hijriYear,
      formatted: `${hijriDay} ${hijriMonths[hijriMonth]} ${hijriYear} هـ`,
    };
  },
};

// تصدير البيانات للاستخدام العام
if (typeof module !== "undefined" && module.exports) {
  module.exports = { ISLAMIC_DATA, IslamicDataHelpers };
}
