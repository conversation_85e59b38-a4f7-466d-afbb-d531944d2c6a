/* نظام التصميم الاحترافي الحديث */

/* تنسيقات التاريخ الهجري */
.current-date {
    font-family: 'Cairo', sans-serif;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
    text-align: center;
}

.hijri-date {
    background: var(--background-glass);
    padding: 4px 12px;
    border-radius: var(--radius-full);
    display: inline-block;
}

/* تنسيقات البوصلة واتجاه القبلة */
.qibla-container {
    text-align: center;
    margin: 2rem 0;
    padding: 1rem;
    background: var(--background-glass);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-primary);
}

.qibla-compass {
    width: 150px;
    height: 150px;
    margin: 1rem auto;
    position: relative;
    border-radius: 50%;
    background: var(--background-card);
    box-shadow: var(--shadow-medium);
    display: flex;
    align-items: center;
    justify-content: center;
}

.qibla-arrow {
    font-size: 2rem;
    color: var(--secondary);
    transition: transform 0.5s ease;
}

.qibla-arrow i {
    filter: drop-shadow(0 0 10px var(--secondary));
}

:root {
    /* الألوان الأساسية - نظام إسلامي أنيق */
    --primary: #2D5A27;
    --primary-hover: #1F3D1A;
    --primary-light: #4A7C59;
    --primary-gradient: linear-gradient(135deg, #2D5A27 0%, #4A7C59 50%, #6B8E23 100%);

    --secondary: #B8860B;
    --secondary-hover: #9A7209;
    --secondary-light: #DAA520;
    --secondary-gradient: linear-gradient(135deg, #B8860B 0%, #DAA520 50%, #FFD700 100%);

    --accent: #8B4513;
    --accent-hover: #654321;
    --accent-light: #A0522D;
    --accent-gradient: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%);

    /* خلفيات متدرجة */
    --background-main: linear-gradient(135deg, #0F1419 0%, #1A202C 25%, #2D3748 50%, #1A202C 75%, #0F1419 100%);
    --background-card: linear-gradient(145deg, #1E2A3A 0%, #2D3E50 100%);
    --background-glass: rgba(255, 255, 255, 0.05);
    --background-glass-hover: rgba(255, 255, 255, 0.1);

    /* نصوص */
    --text-primary: #FFFFFF;
    --text-secondary: #E2E8F0;
    --text-muted: #A0AEC0;
    --text-accent: #F7FAFC;

    /* حدود وظلال */
    --border-primary: rgba(255, 255, 255, 0.1);
    --border-secondary: rgba(255, 255, 255, 0.05);
    --border-accent: rgba(184, 134, 11, 0.3);

    --shadow-small: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
    --shadow-large: 0 8px 32px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(184, 134, 11, 0.3);
    --shadow-primary: 0 4px 20px rgba(45, 90, 39, 0.4);

    /* أنصاف أقطار */
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 24px;
    --radius-full: 50px;

    /* انتقالات */
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* مسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* إعادة تعيين عامة محسنة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* النمط الأساسي المحسن */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--background-main);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    min-height: 100vh;
    overflow-x: hidden;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين الخطوط */
.dhikr-text {
    font-family: 'Amiri', 'Scheherazade New', serif;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.quran-verse {
    font-family: 'Amiri', 'Scheherazade New', serif;
    font-weight: 400;
    line-height: 1.8;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* التطبيق الرئيسي */
.app {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-md);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    position: relative;
}

/* الرأس المحسن */
.header {
    background: var(--background-card);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-large);
    margin-bottom: var(--spacing-xl);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-primary);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-icon {
    font-size: 2.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
    filter: drop-shadow(0 2px 4px rgba(45, 90, 39, 0.3));
}

.header h1 {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 800;
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-controls {
    display: flex;
    gap: var(--spacing-sm);
}

/* أزرار محسنة */
.icon-button {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    min-height: 48px;
    position: relative;
    overflow: hidden;
}

.icon-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.icon-button:hover::before {
    left: 100%;
}

.icon-button:hover {
    background: var(--background-glass-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--border-accent);
}

.icon-button:active {
    transform: translateY(0);
}

/* انيميشن النبض */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* شريط التنقل */
.navigation {
    background: var(--background-card);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-medium);
    margin-bottom: var(--spacing-xl);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-primary);
    position: sticky;
    top: var(--spacing-md);
    z-index: var(--z-sticky);
}

.nav-container {
    display: flex;
    justify-content: space-around;
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
}

.nav-btn {
    background: var(--background-glass);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-medium);
    color: var(--text-secondary);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 80px;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.nav-btn:hover::before {
    transform: scaleX(1);
}

.nav-btn:hover {
    background: var(--background-glass-hover);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.nav-btn.active {
    background: var(--primary-gradient);
    color: var(--text-primary);
    border-color: var(--primary);
    box-shadow: var(--shadow-primary);
}

.nav-btn.active::before {
    transform: scaleX(1);
}

.nav-btn i {
    font-size: 1.25rem;
    transition: var(--transition-fast);
}

.nav-btn span {
    font-size: 0.85rem;
    font-weight: 600;
    transition: var(--transition-fast);
}

.nav-btn:hover i,
.nav-btn.active i {
    transform: scale(1.1);
}

/* نظام الأقسام */
.section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    position: relative;
}

/* بطاقات محسنة */
.card {
    background: var(--background-card);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-large);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition-normal);
}

.card:hover::before {
    opacity: 1;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-large), var(--shadow-glow);
    border-color: var(--border-accent);
}

/* رؤوس الأقسام */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-secondary);
    background: linear-gradient(135deg, var(--background-glass) 0%, transparent 100%);
}

.section-header h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-header h2 i {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.25rem;
}

/* أزرار أساسية محسنة */
.primary-button {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--radius-large);
    color: var(--text-primary);
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: 1.25rem;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 200px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.primary-button:hover::before {
    left: 100%;
}

.primary-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-large), var(--shadow-primary);
    filter: brightness(1.1);
}

.primary-button:active {
    transform: translateY(-1px);
}

/* أزرار ثانوية محسنة */
.secondary-button {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    backdrop-filter: blur(10px);
}

.secondary-button:hover {
    background: var(--secondary-gradient);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: var(--text-primary);
}

/* تحسينات متجاوبة */
@media (max-width: 768px) {
    .app {
        padding: var(--spacing-sm);
        gap: var(--spacing-lg);
    }

    .header-content {
        padding: var(--spacing-md);
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .header-icon {
        font-size: 2rem;
    }

    .section-header {
        padding: var(--spacing-md);
    }

    .primary-button {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
        min-width: 150px;
    }

    /* تحسينات التنقل للموبايل */
    .nav-container {
        padding: var(--spacing-sm);
        gap: var(--spacing-xs);
    }

    .nav-btn {
        padding: var(--spacing-sm);
        min-width: 60px;
        font-size: 0.8rem;
    }

    .nav-btn i {
        font-size: 1rem;
    }

    .nav-btn span {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .logo-section {
        flex-direction: column;
        text-align: center;
    }

    .header-controls {
        justify-content: center;
    }
}

/* تصميم المسبحة المحسن */
.tasbih-container {
    background: var(--background-card);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-large);
    text-align: center;
    transition: var(--transition-normal);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.tasbih-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.tasbih-container::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(45, 90, 39, 0.05) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.tasbih-container:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-large), var(--shadow-glow);
}

/* رأس المسبحة */
.tasbih-header {
    margin-bottom: var(--spacing-2xl);
    position: relative;
    z-index: 1;
}

.dhikr-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-glass);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-secondary);
}

.dhikr-nav-btn {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-full);
    color: var(--text-primary);
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.dhikr-nav-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--primary-gradient);
    border-radius: 50%;
    transition: var(--transition-normal);
    transform: translate(-50%, -50%);
}

.dhikr-nav-btn:hover::before {
    width: 100%;
    height: 100%;
}

.dhikr-nav-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
    border-color: var(--border-accent);
}

.dhikr-nav-btn i {
    position: relative;
    z-index: 1;
    transition: var(--transition-fast);
}

.dhikr-nav-btn:hover i {
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dhikr-text {
    font-size: 2rem;
    color: var(--text-primary);
    font-weight: 800;
    text-align: center;
    min-width: 300px;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--background-glass) 0%, rgba(184, 134, 11, 0.1) 100%);
    border-radius: var(--radius-large);
    border: 2px solid var(--border-accent);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.dhikr-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(184, 134, 11, 0.2), transparent);
    transition: left 1s;
}

.dhikr-text:hover::before {
    left: 100%;
}

/* شريط التقدم المحسن */
.dhikr-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    justify-content: center;
    padding: var(--spacing-lg);
    background: var(--background-glass);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-secondary);
}

.progress-bar {
    flex: 1;
    max-width: 400px;
    height: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    overflow: hidden;
    border: 1px solid var(--border-primary);
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            rgba(255, 255, 255, 0.1) 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    width: 0%;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--radius-full);
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.progress-text {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 600;
    min-width: 80px;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-glass);
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-secondary);
}

/* عرض العداد المحسن */
.tasbih-display {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    position: relative;
    z-index: 1;
}

.counter-container,
.target-container {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--background-glass);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.counter-container::before,
.target-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--secondary-gradient);
}

.counter {
    font-size: 4.5rem;
    font-weight: 900;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(45, 90, 39, 0.3);
    margin-bottom: var(--spacing-sm);
    transition: var(--transition-normal);
    position: relative;
}

.counter.clicked {
    animation: counter-bounce 0.3s ease-out;
}

@keyframes counter-bounce {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

.target-count {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
}

.counter-label,
.target-label {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* أزرار التحكم المحسنة */
.tasbih-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    align-items: center;
    position: relative;
    z-index: 1;
}

.secondary-controls {
    display: flex;
    gap: var(--spacing-md);
}

/* تحسينات متجاوبة للمسبحة */
@media (max-width: 768px) {
    .tasbih-container {
        padding: var(--spacing-xl);
    }

    .dhikr-selector {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .dhikr-text {
        font-size: 1.5rem;
        min-width: 250px;
    }

    .tasbih-display {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .counter {
        font-size: 3.5rem;
    }

    .target-count {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .dhikr-text {
        font-size: 1.25rem;
        min-width: 200px;
        padding: var(--spacing-md);
    }

    .counter {
        font-size: 3rem;
    }

    .target-count {
        font-size: 1.5rem;
    }

    .dhikr-nav-btn {
        width: 40px;
        height: 40px;
    }
}

/* تصميم مواقيت الصلاة المحسن */
.prayer-container {
    background: var(--background-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-large);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
    overflow: hidden;
    position: relative;
}

.prayer-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--secondary-gradient);
}

.prayer-content {
    padding: var(--spacing-xl);
}

.location-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-glass);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-secondary);
}

.city-select {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
}

.city-select:hover {
    border-color: var(--border-accent);
    background: var(--background-glass-hover);
}

.city-select:focus {
    outline: none;
    border-color: var(--secondary);
    box-shadow: 0 0 0 3px rgba(184, 134, 11, 0.2);
}

#current-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-align: center;
}

.date-gregorian {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.date-hijri {
    font-size: 0.8rem;
    opacity: 0.8;
}

.prayer-times {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.prayer-time-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--background-glass);
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-secondary);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.prayer-time-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--accent-gradient);
    transform: scaleY(0);
    transition: var(--transition-normal);
}

.prayer-time-item:hover::before {
    transform: scaleY(1);
}

.prayer-time-item:hover {
    background: var(--background-glass-hover);
    transform: translateX(-5px);
    box-shadow: var(--shadow-medium);
}

.prayer-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.prayer-time {
    font-weight: 700;
    color: var(--secondary);
    font-size: 1.2rem;
    font-family: 'Courier New', monospace;
}

.prayer-countdown {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--background-glass) 0%, rgba(184, 134, 11, 0.1) 100%);
    border-radius: var(--radius-large);
    border: 2px solid var(--border-accent);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.prayer-countdown::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(184, 134, 11, 0.2), transparent);
    animation: countdown-shine 3s infinite;
}

@keyframes countdown-shine {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

.countdown-header {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.countdown-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

#next-prayer-name {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
}

#countdown-timer {
    font-size: 1.5rem;
    font-weight: 800;
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Courier New', monospace;
}

/* تصميم الأذكار المحسن */
.adhkar-container {
    background: var(--background-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-large);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
    overflow: hidden;
    position: relative;
}

.adhkar-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-gradient);
}

.adhkar-controls {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
}

.filter-btn {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.filter-btn:hover {
    background: var(--background-glass-hover);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.filter-btn.active {
    background: var(--accent-gradient);
    color: var(--text-primary);
    border-color: var(--accent);
    box-shadow: var(--shadow-medium);
}

#adhkar-list {
    padding: var(--spacing-xl);
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-accent) transparent;
}

#adhkar-list::-webkit-scrollbar {
    width: 6px;
}

#adhkar-list::-webkit-scrollbar-track {
    background: transparent;
}

#adhkar-list::-webkit-scrollbar-thumb {
    background: var(--border-accent);
    border-radius: var(--radius-full);
}

.dhikr-item {
    background: var(--background-glass);
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-secondary);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.dhikr-item::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: var(--transition-normal);
}

.dhikr-item:hover::before {
    transform: scaleY(1);
}

.dhikr-item:hover {
    background: var(--background-glass-hover);
    transform: translateX(5px);
    box-shadow: var(--shadow-medium);
}

.dhikr-item .dhikr-text {
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: var(--spacing-md);
    font-weight: 500;
}

.dhikr-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.dhikr-count {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.dhikr-reference {
    font-style: italic;
    opacity: 0.8;
}

/* تصميم القرآن الكريم المحسن */
.quran-container {
    background: var(--background-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-large);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
    overflow: hidden;
    position: relative;
}

.quran-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.quran-content {
    padding: var(--spacing-xl);
}

.quran-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-glass);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-secondary);
}

.surah-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.surah-select {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    min-width: 200px;
}

.surah-select:hover {
    border-color: var(--border-accent);
    background: var(--background-glass-hover);
}

.surah-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(45, 90, 39, 0.2);
}

.ayah-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.ayah-select {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    min-width: 80px;
}

.quran-display {
    background: linear-gradient(135deg, var(--background-glass) 0%, rgba(45, 90, 39, 0.1) 100%);
    border-radius: var(--radius-large);
    border: 2px solid var(--border-accent);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quran-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(45, 90, 39, 0.2), transparent);
    animation: quran-shine 4s infinite;
}

@keyframes quran-shine {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

.quran-verse {
    font-size: 1.8rem;
    line-height: 2.2;
    color: var(--text-primary);
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
    max-width: 800px;
}

.verse-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--background-glass);
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-secondary);
    font-size: 0.9rem;
    color: var(--text-secondary);
    position: relative;
    z-index: 1;
}

.quran-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.quran-nav-btn {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    backdrop-filter: blur(10px);
    font-weight: 500;
}

.quran-nav-btn:hover {
    background: var(--primary-gradient);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: var(--text-primary);
}

.quran-action-btn {
    background: var(--secondary-gradient);
    border: none;
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.quran-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    filter: brightness(1.1);
}

/* تصميم الراديو المحسن */
.radio-container {
    background: var(--background-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-large);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
    overflow: hidden;
    position: relative;
}

.radio-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-gradient);
}

.radio-content {
    padding: var(--spacing-xl);
}

.radio-player {
    background: linear-gradient(135deg, var(--background-glass) 0%, rgba(139, 69, 19, 0.1) 100%);
    border-radius: var(--radius-large);
    border: 2px solid var(--border-accent);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.radio-player::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(139, 69, 19, 0.2), transparent);
    animation: radio-pulse 3s infinite;
}

@keyframes radio-pulse {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

.station-info {
    margin-bottom: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.station-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.station-status {
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--error);
    animation: status-blink 2s infinite;
}

.status-indicator.playing {
    background: var(--success);
}

@keyframes status-blink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0.3;
    }
}

.radio-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    position: relative;
    z-index: 1;
}

.radio-btn {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-full);
    color: var(--text-primary);
    width: 60px;
    height: 60px;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    font-size: 1.5rem;
    position: relative;
    overflow: hidden;
}

.radio-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--accent-gradient);
    border-radius: 50%;
    transition: var(--transition-normal);
    transform: translate(-50%, -50%);
}

.radio-btn:hover::before {
    width: 100%;
    height: 100%;
}

.radio-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
    border-color: var(--border-accent);
}

.radio-btn i {
    position: relative;
    z-index: 1;
    transition: var(--transition-fast);
}

.radio-btn:hover i {
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    justify-content: center;
    padding: var(--spacing-lg);
    background: var(--background-glass);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-secondary);
    margin-bottom: var(--spacing-xl);
}

.volume-slider {
    flex: 1;
    max-width: 200px;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--accent-gradient);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-small);
    transition: var(--transition-normal);
}

.volume-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: var(--shadow-medium);
}

.volume-value {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    min-width: 40px;
    text-align: center;
}

.stations-list {
    display: grid;
    gap: var(--spacing-sm);
}

.station-item {
    background: var(--background-glass);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-medium);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.station-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--accent-gradient);
    transform: scaleY(0);
    transition: var(--transition-normal);
}

.station-item:hover::before {
    transform: scaleY(1);
}

.station-item:hover {
    background: var(--background-glass-hover);
    transform: translateX(-5px);
    box-shadow: var(--shadow-medium);
}

.station-item.active {
    background: linear-gradient(135deg, var(--background-glass-hover) 0%, rgba(139, 69, 19, 0.2) 100%);
    border-color: var(--border-accent);
    box-shadow: var(--shadow-medium);
}

.station-item.active::before {
    transform: scaleY(1);
}

.station-title {
    font-weight: 600;
    color: var(--text-primary);
}

.station-description {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* تأثيرات خاصة وانيميشن */
.floating-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--secondary);
    border-radius: 50%;
    opacity: 0.3;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }

    10% {
        opacity: 0.3;
    }

    90% {
        opacity: 0.3;
    }

    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* تأثيرات التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-primary);
    border-radius: 50%;
    border-top-color: var(--secondary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* تأثيرات النجاح */
.success-animation {
    animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

/* تأثيرات الخطأ */
.error-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}

/* إشعارات محسنة */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--background-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-large);
    backdrop-filter: blur(20px);
    z-index: var(--z-toast);
    transform: translateX(100%);
    transition: var(--transition-normal);
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success);
}

.notification.error {
    border-left: 4px solid var(--error);
}

.notification.warning {
    border-left: 4px solid var(--warning);
}

/* تحسينات متجاوبة شاملة */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
    }

    .app {
        padding: var(--spacing-sm);
    }
}

@media (max-width: 768px) {
    .header h1 {
        font-size: 1.5rem;
    }

    .header-icon {
        font-size: 2rem;
    }

    .prayer-navigation,
    .quran-navigation {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .location-info {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .prayer-countdown .countdown-content {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .quran-verse {
        font-size: 1.5rem;
        line-height: 2;
    }

    .radio-controls {
        gap: var(--spacing-md);
    }

    .radio-btn {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .volume-control {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .volume-slider {
        max-width: 150px;
    }

    .adhkar-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .filter-btn {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .logo-section {
        flex-direction: column;
    }

    .header h1 {
        font-size: 1.25rem;
    }

    .section-header {
        padding: var(--spacing-md);
    }

    .section-header h2 {
        font-size: 1.25rem;
    }

    .card {
        margin: 0 -var(--spacing-xs);
    }

    .prayer-content,
    .quran-content,
    .radio-content {
        padding: var(--spacing-lg);
    }

    .prayer-time-item {
        padding: var(--spacing-md);
    }

    .prayer-name,
    .prayer-time {
        font-size: 1rem;
    }

    .quran-verse {
        font-size: 1.25rem;
        line-height: 1.8;
    }

    .quran-controls {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .quran-nav-btn,
    .quran-action-btn {
        width: 100%;
        justify-content: center;
    }

    .station-name {
        font-size: 1.25rem;
    }

    .radio-player {
        padding: var(--spacing-lg);
    }

    .dhikr-item {
        padding: var(--spacing-md);
    }

    .dhikr-item .dhikr-text {
        font-size: 1rem;
        line-height: 1.6;
    }

    .notification {
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        max-width: none;
    }
}

/* تحسينات الطباعة */
@media print {

    .header-controls,
    .tasbih-controls,
    .secondary-controls,
    .prayer-countdown,
    .radio-controls,
    .volume-control,
    .stations-list,
    .adhkar-controls,
    .quran-controls {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .quran-verse,
    .dhikr-text {
        color: #000 !important;
        font-size: 14pt;
        line-height: 1.6;
    }
}

/* تحسينات إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسين التركيز للوحة المفاتيح */
button:focus,
select:focus,
input:focus {
    outline: 2px solid var(--secondary);
    outline-offset: 2px;
}

/* تحسينات الحركة المخفضة */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات الألوان عالية التباين */
@media (prefers-contrast: high) {
    :root {
        --border-primary: rgba(255, 255, 255, 0.3);
        --border-secondary: rgba(255, 255, 255, 0.2);
        --text-muted: #E2E8F0;
    }
}

/* تحسينات الوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #FFFFFF;
        --text-secondary: #E2E8F0;
        --background-main: linear-gradient(135deg, #000000 0%, #1A1A1A 25%, #2D2D2D 50%, #1A1A1A 75%, #000000 100%);
    }
}

/* تأثيرات خاصة للتفاعل */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
    width: 300px;
    height: 300px;
}

/* تحسينات الأداء */
.card,
.primary-button,
.secondary-button,
.icon-button {
    will-change: transform;
}

/* تحسين التمرير */
html {
    scroll-behavior: smooth;
}

.smooth-scroll {
    scroll-behavior: smooth;
}

/* إخفاء شريط التمرير في بعض المتصفحات */
.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

/* نافذة الإعدادات المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
}

.modal-content {
    background: var(--background-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-large);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    z-index: 1;
    transform: scale(0.9) translateY(20px);
    transition: var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-secondary);
    background: linear-gradient(135deg, var(--background-glass) 0%, transparent 100%);
}

.modal-header h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.modal-header h3 i {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.close-btn {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-full);
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: var(--error);
    color: var(--text-primary);
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
}

.header-buttons {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.emergency-close-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border: none;
    border-radius: var(--radius-full);
    color: var(--text-primary);
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-small);
    animation: pulse-emergency 2s infinite;
}

.emergency-close-btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
    animation: none;
}

@keyframes pulse-emergency {

    0%,
    100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }

    50% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
}

.modal-body {
    padding: var(--spacing-xl);
}

.setting-group {
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-lg);
    background: var(--background-glass);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-secondary);
}

.setting-group h4 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-secondary);
}

.setting-group h4 i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--background-glass);
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-secondary);
    transition: var(--transition-normal);
}

.setting-item:hover {
    background: var(--background-glass-hover);
    transform: translateX(-2px);
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    color: var(--text-primary);
    font-weight: 500;
    flex: 1;
}

/* Toggle Switch */
.toggle-container {
    position: relative;
    display: inline-block;
}

.toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-label {
    display: block;
    width: 50px;
    height: 24px;
    background: var(--border-primary);
    border-radius: var(--radius-full);
    position: relative;
    cursor: pointer;
    transition: var(--transition-normal);
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: var(--text-primary);
    border-radius: 50%;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-small);
}

.toggle-input:checked+.toggle-label {
    background: var(--primary-gradient);
}

.toggle-input:checked+.toggle-label .toggle-slider {
    transform: translateX(26px);
}

/* Volume Control */
.volume-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    max-width: 200px;
}

.volume-slider {
    flex: 1;
    height: 6px;
    background: var(--border-primary);
    border-radius: var(--radius-full);
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: var(--secondary-gradient);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-small);
    transition: var(--transition-normal);
}

.volume-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: var(--shadow-medium);
}

.volume-value {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    min-width: 40px;
    text-align: center;
}

/* Font Size Control */
.font-size-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    max-width: 200px;
}

.font-slider {
    flex: 1;
    height: 6px;
    background: var(--border-primary);
    border-radius: var(--radius-full);
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.font-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: var(--accent-gradient);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-small);
    transition: var(--transition-normal);
}

.font-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: var(--shadow-medium);
}

/* Select Input */
.select-input {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    min-width: 150px;
}

.select-input:hover {
    border-color: var(--border-accent);
    background: var(--background-glass-hover);
}

.select-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(45, 90, 39, 0.2);
}

/* Number Input */
.number-input {
    background: var(--background-glass);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    width: 120px;
    text-align: center;
}

.number-input:hover {
    border-color: var(--border-accent);
    background: var(--background-glass-hover);
}

.number-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(45, 90, 39, 0.2);
}

/* Test Button */
.test-btn {
    background: var(--accent-gradient);
    border: none;
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.test-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    filter: brightness(1.1);
}

/* Modal Footer */
.modal-footer {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-secondary);
    background: linear-gradient(135deg, transparent 0%, var(--background-glass) 100%);
}

.modal-footer .primary-button,
.modal-footer .secondary-button {
    flex: 1;
    justify-content: center;
}

/* تأثير الصوت */
.sound-playing {
    animation: sound-pulse 0.3s ease-out;
}

@keyframes sound-pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
        box-shadow: 0 0 20px var(--secondary);
    }

    100% {
        transform: scale(1);
    }
}

/* حماية ضد التلاعب بالـ CSS */
* {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* السماح بالتحديد للحقول النصية فقط */
input[type="text"],
input[type="number"],
textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* حماية ضد طباعة الصفحة */
@media print {
    * {
        display: none !important;
    }

    body::before {
        content: "🛡️ هذا المحتوى محمي ولا يمكن طباعته";
        display: block !important;
        text-align: center;
        font-size: 24px;
        color: #e74c3c;
        padding: 50px;
    }
}

/* حماية ضد تغيير الخطوط */
@font-face {
    font-family: 'protected';
    src: url('data:font/woff2;base64,') format('woff2');
}

/* منع تحديد النصوص الحساسة */
.dhikr-text,
.ayah-text,
.prayer-time {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: auto;
}

/* حماية ضد فحص العناصر */
.tasbih-container,
.prayer-container,
.quran-container {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* إخفاء شريط التمرير في وضع المطور */
::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

/* حماية ضد تغيير الألوان */
* {
    color-scheme: light !important;
}

/* منع التلاعب بالعناصر المهمة */
.nav-btn,
.primary-button,
.secondary-button {
    pointer-events: auto !important;
    position: relative !important;
}

/* حماية ضد إخفاء العناصر */
.section,
.modal,
.notification {
    visibility: visible !important;
    opacity: 1 !important;
}

/* تأمين الخطوط */
body,
* {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* حماية ضد تغيير الخلفيات */
body {
    background: var(--background-primary) !important;
}

/* منع التلاعب بالـ z-index */
.modal {
    z-index: 99999 !important;
}

.security-warning {
    z-index: 100000 !important;
}

/* تحسينات قسم التسبيح */
.tasbih-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tasbih-button.clicked {
    transform: scale(0.95);
    box-shadow: 0 8px 25px rgba(45, 90, 39, 0.4);
}

.tasbih-button:active {
    transform: scale(0.9);
}

/* تأثير الريبل */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* تحسين شريط التقدم */
.progress-fill {
    transition: width 0.3s ease, background 0.3s ease;
}

.progress-fill.completed {
    background: linear-gradient(90deg, #27ae60, #2ecc71, #f39c12, #e74c3c);
    animation: progress-celebration 1s ease-in-out;
}

@keyframes progress-celebration {

    0%,
    100% {
        transform: scaleY(1);
    }

    50% {
        transform: scaleY(1.2);
    }
}

/* تحسين عرض الذكر */
.dhikr-text {
    transition: font-size 0.3s ease, color 0.3s ease;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسين الإحصائيات */
.stats-container {
    animation: slideDown 0.3s ease-out;
}

.stats-container.hidden {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }

    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* تحسين الأزرار الثانوية */
.secondary-button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.secondary-button:active {
    transform: translateY(0) scale(0.95);
}

/* تحسين العداد */
.counter {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.counter-container:hover .counter {
    transform: scale(1.1);
    color: var(--primary);
}

/* تحسين العدد المستهدف */
.target-count {
    transition: all 0.3s ease;
}

.target-container:hover .target-count {
    transform: scale(1.05);
    color: var(--secondary);
}

/* تأثيرات الإنجازات */
.achievement-notification {
    animation: achievementSlide 0.5s ease-out;
}

@keyframes achievementSlide {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسين أزرار التنقل بين الأذكار */
.dhikr-nav-btn {
    transition: all 0.3s ease;
    position: relative;
}

.dhikr-nav-btn:hover {
    transform: scale(1.1);
    background: var(--primary-gradient);
    color: var(--text-primary);
}

.dhikr-nav-btn:active {
    transform: scale(0.9);
}

/* تحسين مظهر التسبيح العام */
.tasbih-container {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين الاستجابة للمس */
@media (hover: none) and (pointer: coarse) {
    .tasbih-button {
        min-height: 80px;
        font-size: 1.2rem;
    }

    .secondary-button {
        min-width: 50px;
        min-height: 50px;
    }

    .dhikr-nav-btn {
        min-width: 45px;
        min-height: 45px;
    }
}