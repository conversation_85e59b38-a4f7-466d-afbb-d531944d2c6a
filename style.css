/* المتغيرات العامة */
:root {
    --primary-color: #4141f7;
    --secondary-color: #333;
    --text-color: #fff;
    --bg-color: #4141f7;
    --section-bg: rgba(0, 0, 0, 0.2);
    --border-color: rgba(255, 255, 255, 0.1);
    --header-height: 60px;
    --primary: #4F46E5;
    --primary-hover: #4338CA;
    --primary-light: #818CF8;
    --secondary: #10B981;
    --secondary-hover: #059669;
    --accent: #F59E0B;
    --accent-hover: #D97706;
    --background: #0F172A;
    --background-light: #1E293B;
    --card: #1E293B;
    --card-hover: #334155;
    --card-light: #475569;
    --text: #F8FAFC;
    --text-secondary: #CBD5E1;
    --text-muted: #94A3B8;
    --border: #334155;
    --border-light: #475569;
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    --shadow: rgba(0, 0, 0, 0.25);
    --shadow-light: rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary) 0%, var(--accent) 100%);
    --border-radius: 12px;
    --border-radius-small: 8px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعادة تعيين عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* النمط الأساسي */
body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--background) 0%, var(--background-light) 100%);
    color: var(--text);
    line-height: 1.6;
    direction: rtl;
    min-height: 100vh;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* تحسين الخطوط */
.dhikr-text {
    font-family: 'Amiri', 'Scheherazade New', serif;
    font-weight: 700;
}

.quran-verse {
    font-family: 'Amiri', 'Scheherazade New', serif;
    font-weight: 400;
}

/* تحسينات عامة */
.app {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* الرأس */
.header {
    background: var(--card);
    border-radius: var(--border-radius);
    box-shadow: 0 8px 32px var(--shadow);
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid var(--border);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    font-size: 2rem;
    color: var(--primary);
    animation: pulse 2s infinite;
}

.header h1 {
    color: var(--text);
    font-size: 1.75rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

.icon-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border);
    border-radius: var(--border-radius-small);
    color: var(--text);
    padding: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
}

.icon-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow);
}

.icon-button:active {
    transform: translateY(0);
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    display: grid;
    gap: 1.5rem;
}

/* المسبحة */
.tasbih-container {
    background: var(--card);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 12px 48px var(--shadow);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--border);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.tasbih-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
}

.tasbih-container:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 64px var(--shadow);
}

/* رأس المسبحة */
.tasbih-header {
    margin-bottom: 2rem;
}

.dhikr-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.dhikr-nav-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border);
    border-radius: 50%;
    color: var(--text);
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.dhikr-nav-btn:hover {
    background: var(--primary);
    transform: scale(1.1);
}

.dhikr-text {
    font-size: 1.5rem;
    color: var(--text);
    font-weight: 700;
    text-align: center;
    min-width: 200px;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-small);
    border: 1px solid var(--border);
}

.dhikr-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: center;
}

.progress-bar {
    flex: 1;
    max-width: 300px;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--border);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 4px;
}

.progress-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    min-width: 60px;
}

/* عرض العداد */
.tasbih-display {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 2rem;
}

.counter-container,
.target-container {
    text-align: center;
}

.counter {
    font-size: 4rem;
    font-weight: 900;
    color: var(--primary);
    text-shadow: 0 0 30px rgba(79, 70, 229, 0.5);
    margin-bottom: 0.5rem;
    transition: var(--transition);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.target-count {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent);
    margin-bottom: 0.5rem;
}

.counter-label,
.target-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
}

/* أزرار التحكم */
.tasbih-controls {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

.primary-button {
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--border-radius);
    color: var(--text);
    padding: 1.5rem 3rem;
    font-size: 1.25rem;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 200px;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.primary-button:hover::before {
    left: 100%;
}

.primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);
}

.primary-button:active {
    transform: translateY(0);
}

.secondary-controls {
    display: flex;
    gap: 1rem;
}

.secondary-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border);
    border-radius: var(--border-radius-small);
    color: var(--text);
    padding: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
}

.secondary-button:hover {
    background: var(--secondary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* تحسين مواقيت الصلاة */
.prayer-container {
    background: var(--card);
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.prayer-container h2 {
    color: var(--primary);
    margin-bottom: 1rem;
    text-align: center;
}

.prayer-times {
    display: grid;
    gap: 0.8rem;
}

.prayer-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.prayer-time:hover {
    background: rgba(255, 255, 255, 0.05);
}

.prayer-time:last-child {
    border-bottom: none;
}

.current-prayer {
    background: rgba(129, 140, 248, 0.1);
    border-radius: 0.5rem;
}

.next-prayer {
    background: rgba(124, 58, 237, 0.1);
    border-radius: 0.5rem;
}

.prayer-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    background: var(--primary);
    color: var(--text);
}

.prayer-status.next {
    background: var(--accent);
}

.prayer-countdown {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(129, 140, 248, 0.1);
    border-radius: 0.5rem;
    text-align: center;
}

.countdown-value {
    color: var(--primary);
    font-weight: bold;
    font-size: 1.2rem;
}

/* الراديو */
.radio-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.radio-player {
    background: var(--card);
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.station-selector {
    width: 100%;
}

.station-select {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    color: var(--text);
    cursor: pointer;
    transition: all 0.3s ease;
}

.station-select:hover {
    background: rgba(255, 255, 255, 0.08);
}

.radio-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.play-btn {
    background: var(--primary);
    color: var(--text);
    border: none;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.play-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.volume-control {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.volume-slider {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.1);
    -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary);
    cursor: pointer;
    transition: all 0.3s;
}

.volume-slider::-webkit-slider-thumb:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

.status-text {
    display: block;
    text-align: center;
    margin-top: 1rem;
    color: var(--text-secondary);
}

.status-text.error {
    color: #ef4444;
}

/* تأثيرات حركية */
.clicked {
    animation: click 0.2s ease-in-out;
}

@keyframes click {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0.95);
    }

    100% {
        transform: scale(1);
    }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 600px) {
    .tasbih-controls {
        flex-direction: column;
    }

    .counter {
        font-size: 4rem;
    }

    .dhikr-content {
        font-size: 1rem;
    }
}

/* تنسيقات قسم الراديو */
.radio-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.radio-player {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.station-selector {
    width: 100%;
}

.station-select {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    color: var(--text);
    cursor: pointer;
    transition: all 0.3s ease;
}

.station-select:hover {
    background: rgba(255, 255, 255, 0.08);
}

.radio-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.play-btn {
    background: var(--primary);
    color: var(--text);
    border: none;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.play-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.volume-control {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.volume-slider {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.1);
    -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary);
    cursor: pointer;
    transition: all 0.3s;
}

.volume-slider::-webkit-slider-thumb:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

.status-text {
    display: block;
    text-align: center;
    margin-top: 1rem;
    color: var(--text-secondary);
}

.status-text.error {
    color: #ef4444;
}

.prayer-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    background: rgba(76, 175, 80, 0.95);
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    animation: slideIn 0.5s ease-out;
    display: flex;
    align-items: center;
    gap: 10px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.prayer-notification.hide {
    animation: slideOut 0.5s ease-in forwards;
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }

    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.prayer-countdown {
    margin-top: 15px;
    padding: 15px;
    background: rgba(33, 150, 243, 0.1);
    border-radius: 8px;
    text-align: center;
    border: 1px solid rgba(33, 150, 243, 0.2);
}

.prayer-countdown span {
    color: #2196F3;
    font-size: 0.9rem;
}

.prayer-countdown strong {
    display: block;
    color: #2196F3;
    font-size: 1.2rem;
    margin-top: 5px;
}

/* إحصائيات وإنجازات */
.stats-container {
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    transition: var(--transition);
}

.stats-container.hidden {
    display: none;
}

.stats-container h3 {
    color: var(--text);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: var(--border-radius-small);
    text-align: center;
    border: 1px solid var(--border);
    transition: var(--transition);
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.stat-value {
    font-size: 2rem;
    font-weight: 900;
    color: var(--primary);
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
}

/* الإنجازات */
.achievement-notification {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--card);
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideIn 0.5s ease-out;
    z-index: 1000;
}

.achievement-notification.hide {
    animation: slideOut 0.5s ease-in forwards;
}

.achievement-icon {
    font-size: 2rem;
}

.achievement-content h3 {
    color: var(--primary);
    margin-bottom: 0.25rem;
}

.achievement-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }

    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* الأذكار */
.adhkar-container {
    background: var(--card);
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    margin-top: 1.5rem;
}

.adhkar-container h3 {
    color: var(--primary);
    margin-bottom: 1rem;
    text-align: center;
}

.dhikr-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s;
}

.dhikr-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.dhikr-item.completed {
    background: rgba(129, 140, 248, 0.1);
}

.dhikr-text {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.dhikr-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.count-badge {
    background: var(--primary);
    color: var(--text);
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    height: 4px;
    border-radius: 2px;
    overflow: hidden;
}

.progress {
    background: var(--primary);
    height: 100%;
    width: 0;
    transition: width 0.3s ease;
}

.completed .progress {
    background: var(--accent);
}

/* العناصر المشتركة */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    color: var(--text);
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-header h2 i {
    color: var(--primary);
}

/* أزرار التصفية */
.adhkar-controls {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border);
    border-radius: var(--border-radius-small);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    font-weight: 600;
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text);
}

.filter-btn.active {
    background: var(--primary);
    color: var(--text);
    border-color: var(--primary);
}

/* معلومات الموقع */
.location-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-small);
    border: 1px solid var(--border);
}

#current-location {
    color: var(--text);
    font-weight: 600;
}

#current-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
    opacity: 1;
    visibility: visible;
    transition: var(--transition);
}

.modal.hidden {
    opacity: 0;
    visibility: hidden;
}

.modal-content {
    background: var(--card);
    border-radius: var(--border-radius);
    box-shadow: 0 20px 80px var(--shadow);
    border: 1px solid var(--border);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(1);
    transition: var(--transition);
}

.modal.hidden .modal-content {
    transform: scale(0.9);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
}

.modal-header h3 {
    color: var(--text);
    font-size: 1.25rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border);
    border-radius: 50%;
    color: var(--text);
    width: 36px;
    height: 36px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--error);
    transform: scale(1.1);
}

.modal-body {
    padding: 1.5rem;
}

/* مجموعات الإعدادات */
.setting-group {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.setting-group label {
    font-weight: 600;
    color: var(--text);
    flex: 1;
}

.switch {
    position: relative;
    display: inline-block;
    width: 52px;
    height: 26px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #4B5563;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    right: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked+.slider {
    background-color: #10B981;
}

input:checked+.slider:before {
    transform: translateX(-26px);
}

.font-size-control {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.font-slider {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: #4B5563;
    outline: none;
    -webkit-appearance: none;
}

.font-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: #10B981;
    border-radius: 50%;
    cursor: pointer;
}

#font-size-value {
    min-width: 50px;
    text-align: center;
    color: var(--text-secondary);
}

/* تنسيق خاص للمفاتيح */
.setting-group .switch {
    flex: 0 0 auto;
    margin-left: 1rem;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 10;
}

/* تنسيق خاص لشريط حجم الخط */
.setting-group input[type="range"] {
    flex: 1;
    margin: 0 1rem;
}

.setting-group #font-size-value {
    flex: 0 0 auto;
    color: var(--text-secondary);
    font-weight: 600;
    min-width: 50px;
}

.setting-group select:focus,
.setting-group input[type="number"]:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* مفاتيح التبديل */
.switch {
    position: relative;
    display: inline-block !important;
    width: 60px;
    height: 34px;
    vertical-align: middle;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100;
}

/* تأكيد ظهور مفاتيح الإعدادات */
#vibration-toggle,
#notifications-toggle {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.setting-group label.switch {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.3) !important;
    transition: var(--transition);
    border-radius: 34px;
    border: 2px solid var(--primary) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.slider:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: var(--primary);
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 2px;
    bottom: 2px;
    background-color: var(--text);
    transition: var(--transition);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

input:checked+.slider {
    background-color: var(--primary);
    border-color: var(--primary);
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.4);
}

input:checked+.slider:before {
    transform: translateX(26px);
    background-color: #ffffff;
}

input:focus+.slider {
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);
}

/* تنسيق المفاتيح الجديدة */
.toggle-container {
    display: inline-block;
    position: relative;
    width: 60px;
    height: 34px;
}

.toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.toggle-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
    display: block;
}

.toggle-label:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.toggle-input:checked+.toggle-label {
    background-color: #4F46E5;
}

.toggle-input:checked+.toggle-label:before {
    transform: translateX(26px);
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

/* شريط تمرير حجم الخط */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--text-color);
    border-radius: 50%;
    cursor: pointer;
}

/* التذييل */
.footer {
    margin-top: 3rem;
    padding: 2rem;
    background: var(--card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.quran-verse {
    font-size: 1.5rem;
    color: var(--primary);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* تأثيرات النقر */
.clicked {
    animation: clickEffect 0.3s ease-in-out;
}

@keyframes clickEffect {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0.95);
    }

    100% {
        transform: scale(1);
    }
}

/* تأثيرات الإنجازات */
.achievement-notification {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--card);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 12px 48px var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideInRight 0.5s ease-out;
    z-index: 1000;
    border: 1px solid var(--border);
    max-width: 350px;
}

.achievement-notification.hidden {
    animation: slideOutRight 0.5s ease-in forwards;
}

.achievement-icon {
    font-size: 3rem;
    animation: bounce 1s infinite;
}

.achievement-content h4 {
    color: var(--primary);
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.achievement-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }

    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

/* تحسينات الراديو */
.volume-value {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    min-width: 40px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-muted);
    transition: var(--transition);
}

.status-dot.playing {
    background: var(--success);
    animation: pulse 1.5s infinite;
}

.status-dot.error {
    background: var(--error);
}

.current-station {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-style: italic;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .app {
        padding: 0.5rem;
    }

    .header-content {
        padding: 1rem;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .tasbih-display {
        flex-direction: column;
        gap: 1.5rem;
    }

    .counter {
        font-size: 3rem;
    }

    .primary-button {
        padding: 1.25rem 2rem;
        font-size: 1.1rem;
    }

    .secondary-controls {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .adhkar-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .achievement-notification {
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

@media (max-width: 480px) {
    .dhikr-selector {
        flex-direction: column;
        gap: 0.75rem;
    }

    .dhikr-text {
        min-width: auto;
        width: 100%;
    }

    .progress-bar {
        max-width: 200px;
    }

    .counter {
        font-size: 2.5rem;
    }

    .target-count {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* تحسينات الأذكار */
.dhikr-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border);
    border-radius: var(--border-radius-small);
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: var(--transition);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dhikr-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow);
}

.dhikr-item:active {
    transform: translateY(0);
}

.dhikr-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dhikr-item .dhikr-text {
    color: var(--text);
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5;
    font-family: 'Amiri', 'Scheherazade New', serif;
}

.dhikr-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.dhikr-actions {
    display: flex;
    gap: 0.5rem;
}

.dhikr-select-btn {
    background: var(--primary);
    border: none;
    border-radius: 50%;
    color: var(--text);
    width: 36px;
    height: 36px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.dhikr-select-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.dhikr-select-btn:active {
    transform: scale(0.95);
}

/* تصنيفات الأذكار */
.dhikr-item[data-category="morning"] {
    border-left: 4px solid var(--accent);
}

.dhikr-item[data-category="evening"] {
    border-left: 4px solid var(--secondary);
}

.dhikr-item[data-category="sleep"] {
    border-left: 4px solid #8B5CF6;
}

.dhikr-item[data-category="general"] {
    border-left: 4px solid var(--primary);
}

/* تحسينات أزرار التصفية */
.filter-btn {
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.filter-btn:hover::before {
    left: 100%;
}

.filter-btn.active {
    background: var(--gradient-primary);
    color: var(--text);
    border-color: var(--primary);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* تحسينات للأجهزة المحمولة - الأذكار */
@media (max-width: 768px) {
    .dhikr-item {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .dhikr-content {
        width: 100%;
    }

    .dhikr-actions {
        align-self: flex-end;
    }

    .dhikr-item .dhikr-text {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .adhkar-controls {
        gap: 0.25rem;
    }

    .filter-btn {
        padding: 0.4rem 0.75rem;
        font-size: 0.8rem;
    }

    .dhikr-item .dhikr-text {
        font-size: 0.9rem;
    }
}

/* الفئات المخفية */
.hidden {
    display: none !important;
}