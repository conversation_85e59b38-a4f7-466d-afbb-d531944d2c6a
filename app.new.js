class TasbihApp {
  constructor() {
    // تهيئة المتغيرات الأساسية
    this.state = {
      counter: 0,
      maxCount: 33,
      currentDhikr: "سبحان الله",
      soundEnabled: true,
      soundVolume: 0.7,
      vibrationEnabled: true,
      notificationsEnabled: true,
      fontSize: 16,
      theme: "light",
      adhkar: [
        "سبحان الله",
        "الحمد لله",
        "لا إله إلا الله",
        "الله أكبر",
        "لا حول ولا قوة إلا بالله",
        "أستغفر الله",
        "سبحان الله وبحمده",
        "سبحان الله العظيم",
      ],
      stats: {
        totalCount: 0,
        todayCount: 0,
        streakCount: 0,
        completedRounds: 0,
        lastActiveDate: null,
      },
      achievements: [],
    };

    // تهيئة الصوت
    this.sounds = {};

    // تهيئة مواقيت الصلاة
    this.prayerTimes = {};

    // تهيئة القرآن
    this.quranData = null;

    // البدء بالتهيئة
    this.initialize();
  }

  async initialize() {
    try {
      console.log("🔄 بدء تهيئة التطبيق...");

      // تحميل الحالة المحفوظة
      this.loadSavedState();

      // تهيئة العناصر والأحداث
      this.initializeElements();
      this.setupEventListeners();

      // تحميل الموارد
      await Promise.all([
        this.loadSounds(),
        this.loadPrayerTimes(),
        this.loadQuranData(),
      ]);

      // إعداد الأقسام المختلفة
      this.setupAdhkarSection();
      this.setupRadioSection();
      this.setupQuranSection();

      // تحديث العرض
      this.updateDisplay();
      this.updateProgress();
      this.updateStats();
      this.updateCurrentDate();

      // تحديث المظهر
      this.applyTheme();

      // تهيئة الإحصائيات اليومية
      this.initializeDailyStats();

      console.log("✅ تم تهيئة التطبيق بنجاح");
    } catch (error) {
      console.error("خطأ في تهيئة التطبيق:", error);
    }
  }

  // دالة تهيئة الإحصائيات اليومية
  initializeDailyStats() {
    const today = new Date().toDateString();
    const lastDate = localStorage.getItem("lastStatsDate");

    if (lastDate !== today) {
      // يوم جديد - إعادة تعيين إحصائيات اليوم
      this.state.stats.todayCount = 0;
      localStorage.setItem("lastStatsDate", today);
      this.saveState();
      console.log("📅 تم إعادة تعيين إحصائيات اليوم");
    }
  }

  initializeElements() {
    // عناصر المسبحة
    this.elements = {
      counter: document.getElementById("counter"),
      dhikrText: document.getElementById("dhikr-text"),
      tasbihButton: document.getElementById("tasbih-button"),
      nextDhikrButton: document.getElementById("next-dhikr"),
      prevDhikrButton: document.getElementById("prev-dhikr"),
      resetButton: document.getElementById("reset-button"),
      statsButton: document.getElementById("stats-button"),
      progressBar: document.getElementById("progress-fill"),
      progressText: document.getElementById("progress-text"),
      statsContainer: document.getElementById("stats-container"),

      // عناصر مواقيت الصلاة
      prayerTimes: document.getElementById("prayer-times"),
      nextPrayerName: document.getElementById("next-prayer-name"),
      countdownTimer: document.getElementById("countdown-timer"),

      // عناصر الراديو
      stationSelect: document.getElementById("station-select"),
      playButton: document.getElementById("play-btn"),
      volumeSlider: document.getElementById("volume-slider"),
      radioStatus: document.getElementById("radio-status"),

      // عناصر القرآن
      surahSelect: document.getElementById("surah-select"),
      ayahSelect: document.getElementById("ayah-select"),
      ayahText: document.getElementById("ayah-text"),
      ayahNumber: document.getElementById("ayah-number"),
      surahName: document.getElementById("surah-name"),
      prevAyahButton: document.getElementById("prev-ayah"),
      nextAyahButton: document.getElementById("next-ayah"),
      copyAyahButton: document.getElementById("copy-ayah"),
      bookmarkAyahButton: document.getElementById("bookmark-ayah"),
    };
  }

  setupEventListeners() {
    // أحداث المسبحة
    this.elements.tasbihButton?.addEventListener("click", () =>
      this.increment()
    );
    this.elements.nextDhikrButton?.addEventListener("click", () =>
      this.nextDhikr()
    );
    this.elements.prevDhikrButton?.addEventListener("click", () =>
      this.prevDhikr()
    );
    this.elements.resetButton?.addEventListener("click", () => this.reset());
    this.elements.statsButton?.addEventListener("click", () =>
      this.toggleStats()
    );

    // أحداث الراديو
    this.elements.stationSelect?.addEventListener("change", () =>
      this.changeStation()
    );
    this.elements.playButton?.addEventListener("click", () =>
      this.toggleRadio()
    );
    this.elements.volumeSlider?.addEventListener("input", (e) =>
      this.changeVolume(e.target.value)
    );

    // أحداث القرآن
    this.elements.surahSelect?.addEventListener("change", () =>
      this.changeSurah()
    );
    this.elements.ayahSelect?.addEventListener("change", () =>
      this.changeAyah()
    );
    this.elements.prevAyahButton?.addEventListener("click", () =>
      this.prevAyah()
    );
    this.elements.nextAyahButton?.addEventListener("click", () =>
      this.nextAyah()
    );
    this.elements.copyAyahButton?.addEventListener("click", () =>
      this.copyAyah()
    );
    this.elements.bookmarkAyahButton?.addEventListener("click", () =>
      this.bookmarkAyah()
    );

    // دعم لوحة المفاتيح
    document.addEventListener("keyup", (e) => {
      if (e.code === "Space") {
        e.preventDefault();
        this.increment();
      } else if (e.code === "ArrowRight") {
        this.prevDhikr();
      } else if (e.code === "ArrowLeft") {
        this.nextDhikr();
      } else if (e.code === "KeyR") {
        this.reset();
      }
    });
  }

  // وظائف التسبيح
  increment() {
    this.state.counter++;
    this.state.stats.totalCount++;
    this.state.stats.todayCount++;

    // تحديث العرض
    this.updateDisplay();
    this.updateProgress();

    // تشغيل الصوت
    this.playSound("click");

    // تحقق من الإنجازات
    this.checkAchievements();

    // حفظ الحالة
    this.saveState();

    // إضافة تأثير بصري
    this.addClickEffect();
  }

  nextDhikr() {
    const currentIndex = this.state.adhkar.indexOf(this.state.currentDhikr);
    const nextIndex = (currentIndex + 1) % this.state.adhkar.length;
    this.state.currentDhikr = this.state.adhkar[nextIndex];
    this.updateDisplay();
    this.saveState();
  }

  prevDhikr() {
    const currentIndex = this.state.adhkar.indexOf(this.state.currentDhikr);
    const prevIndex =
      currentIndex === 0 ? this.state.adhkar.length - 1 : currentIndex - 1;
    this.state.currentDhikr = this.state.adhkar[prevIndex];
    this.updateDisplay();
    this.saveState();
  }

  reset() {
    this.state.counter = 0;
    this.updateDisplay();
    this.updateProgress();
    this.saveState();
    this.playSound("complete");
  }

  toggleStats() {
    this.elements.statsContainer?.classList.toggle("hidden");
  }

  updateDisplay() {
    // تحديث العداد
    if (this.elements.counter) {
      this.elements.counter.textContent = this.state.counter;
    }

    // تحديث نص الذكر
    if (this.elements.dhikrText) {
      this.elements.dhikrText.textContent = this.state.currentDhikr;
      // تطبيق حجم الخط المحفوظ
      if (this.state.fontSize) {
        this.elements.dhikrText.style.fontSize = `${this.state.fontSize}px`;
      }
    }

    // تحديث العدد المستهدف في العرض
    const targetCountElement = document.getElementById("target-count");
    if (targetCountElement) {
      targetCountElement.textContent = this.state.maxCount;
    }
  }

  updateProgress() {
    const progress = (this.state.counter / this.state.maxCount) * 100;

    // تحديث شريط التقدم
    const progressFill = document.getElementById("progress-fill");
    if (progressFill) {
      progressFill.style.width = `${Math.min(progress, 100)}%`;
    }

    // تحديث نص التقدم
    const progressText = document.getElementById("progress-text");
    if (progressText) {
      progressText.textContent = `${this.state.counter} / ${this.state.maxCount}`;
    }

    // إضافة تأثير بصري عند اكتمال الهدف
    if (this.state.counter >= this.state.maxCount) {
      progressFill?.classList.add("completed");
      setTimeout(() => {
        progressFill?.classList.remove("completed");
      }, 1000);
    }
  }

  updateStats() {
    // تحديث عناصر الإحصائيات
    const totalCountElement = document.getElementById("total-count");
    const todayCountElement = document.getElementById("today-count");
    const streakCountElement = document.getElementById("streak-count");
    const completedRoundsElement = document.getElementById("completed-rounds");

    if (totalCountElement) {
      totalCountElement.textContent = this.state.stats.totalCount || 0;
    }
    if (todayCountElement) {
      todayCountElement.textContent = this.state.stats.todayCount || 0;
    }
    if (streakCountElement) {
      streakCountElement.textContent = this.state.stats.streakCount || 0;
    }
    if (completedRoundsElement) {
      completedRoundsElement.textContent =
        this.state.stats.completedRounds || 0;
    }
  }

  addClickEffect() {
    const tasbihButton = document.getElementById("tasbih-button");
    if (tasbihButton) {
      // إضافة تأثير النقر
      tasbihButton.classList.add("clicked");
      setTimeout(() => {
        tasbihButton.classList.remove("clicked");
      }, 150);

      // تفعيل الاهتزاز إذا كان مدعوماً ومفعلاً
      if (this.state.vibrationEnabled && navigator.vibrate) {
        navigator.vibrate(50);
      }

      // إضافة تأثير الريبل
      this.createRippleEffect(tasbihButton);
    }
  }

  createRippleEffect(element) {
    const ripple = document.createElement("span");
    ripple.classList.add("ripple");

    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = rect.width / 2 - size / 2;
    const y = rect.height / 2 - size / 2;

    ripple.style.width = ripple.style.height = size + "px";
    ripple.style.left = x + "px";
    ripple.style.top = y + "px";

    element.appendChild(ripple);

    setTimeout(() => {
      ripple.remove();
    }, 600);
  }

  checkAchievements() {
    // تحقق من إنجاز إكمال الجولة
    if (this.state.counter >= this.state.maxCount) {
      this.state.stats.completedRounds++;
      this.state.counter = 0;
      this.playSound("complete");
      this.showAchievement(
        "جولة مكتملة!",
        `تم إكمال ${this.state.stats.completedRounds} جولة`
      );
    }

    // تحقق من إنجازات أخرى
    if (this.state.stats.totalCount === 1000) {
      this.showAchievement("ألف تسبيحة!", "تهانينا على الوصول لألف تسبيحة");
    }
  }

  showAchievement(title, description) {
    const notification = document.getElementById("achievement-notification");
    const titleElement = document.getElementById("achievement-title");
    const descElement = document.getElementById("achievement-description");

    if (notification && titleElement && descElement) {
      titleElement.textContent = title;
      descElement.textContent = description;
      notification.classList.remove("hidden");

      setTimeout(() => {
        notification.classList.add("hidden");
      }, 3000);
    }
  }

  // وظائف القرآن الكريم
  async loadQuranData() {
    try {
      // تحميل بيانات القرآن
      await import("./quran-data.js");
      this.quranData = window.fullQuranData;

      // تهيئة قائمة السور
      if (this.elements.surahSelect) {
        const options = Object.entries(this.quranData)
          .map(([number, surah]) => {
            return `<option value="${number}">${number}. ${surah.name}</option>`;
          })
          .join("");
        this.elements.surahSelect.innerHTML = options;
      }

      // إخفاء رسالة التحميل
      const loadingStatus = document.getElementById("quran-loading-status");
      if (loadingStatus) {
        loadingStatus.classList.add("hidden");
      }

      // تحميل آخر سورة تم قراءتها
      const lastSurah = localStorage.getItem("lastSurah") || "1";
      const lastAyah = localStorage.getItem("lastAyah") || "1";
      this.changeSurah(lastSurah, lastAyah);
    } catch (error) {
      console.error("خطأ في تحميل بيانات القرآن:", error);
    }
  }

  changeSurah(surahNumber = null, ayahNumber = "1") {
    try {
      // الحصول على رقم السورة
      surahNumber = surahNumber || this.elements.surahSelect.value;
      const surah = this.quranData[surahNumber];

      // تحديث قائمة الآيات
      if (this.elements.ayahSelect) {
        const options = surah.verses
          .map((_, index) => {
            const ayahNum = index + 1;
            return `<option value="${ayahNum}">${ayahNum}</option>`;
          })
          .join("");
        this.elements.ayahSelect.innerHTML = options;
        this.elements.ayahSelect.value = ayahNumber;
      }

      // عرض الآية المحددة
      this.changeAyah(ayahNumber);

      // تحديث اسم السورة
      if (this.elements.surahName) {
        this.elements.surahName.textContent = `سورة ${surah.name}`;
      }

      // حفظ الموضع
      localStorage.setItem("lastSurah", surahNumber);
    } catch (error) {
      console.error("خطأ في تغيير السورة:", error);
    }
  }

  changeAyah(ayahNumber = null) {
    try {
      // الحصول على رقم الآية
      ayahNumber = ayahNumber || this.elements.ayahSelect.value;
      const surahNumber = this.elements.surahSelect.value;
      const surah = this.quranData[surahNumber];
      const ayahIndex = parseInt(ayahNumber) - 1;

      // عرض نص الآية
      if (this.elements.ayahText) {
        this.elements.ayahText.textContent = surah.verses[ayahIndex];
      }

      // تحديث رقم الآية
      if (this.elements.ayahNumber) {
        this.elements.ayahNumber.textContent = `آية ${ayahNumber}`;
      }

      // حفظ الموضع
      localStorage.setItem("lastAyah", ayahNumber);
    } catch (error) {
      console.error("خطأ في تغيير الآية:", error);
    }
  }

  nextAyah() {
    try {
      const currentSurah = this.elements.surahSelect.value;
      const currentAyah = parseInt(this.elements.ayahSelect.value);
      const surah = this.quranData[currentSurah];

      if (currentAyah < surah.verses.length) {
        // الانتقال إلى الآية التالية في نفس السورة
        this.changeAyah(currentAyah + 1);
      } else if (currentSurah < Object.keys(this.quranData).length) {
        // الانتقال إلى السورة التالية
        this.changeSurah(parseInt(currentSurah) + 1, "1");
      }
    } catch (error) {
      console.error("خطأ في الانتقال إلى الآية التالية:", error);
    }
  }

  prevAyah() {
    try {
      const currentSurah = this.elements.surahSelect.value;
      const currentAyah = parseInt(this.elements.ayahSelect.value);

      if (currentAyah > 1) {
        // الانتقال إلى الآية السابقة في نفس السورة
        this.changeAyah(currentAyah - 1);
      } else if (currentSurah > 1) {
        // الانتقال إلى آخر آية في السورة السابقة
        const prevSurah = this.quranData[currentSurah - 1];
        this.changeSurah(currentSurah - 1, prevSurah.verses.length.toString());
      }
    } catch (error) {
      console.error("خطأ في الانتقال إلى الآية السابقة:", error);
    }
  }

  async copyAyah() {
    try {
      const surahNumber = this.elements.surahSelect.value;
      const ayahNumber = this.elements.ayahSelect.value;
      const surah = this.quranData[surahNumber];
      const ayahText = surah.verses[ayahNumber - 1];
      const fullText = `${ayahText} [${surah.name}: ${ayahNumber}]`;

      await navigator.clipboard.writeText(fullText);

      // إظهار رسالة نجاح
      this.showNotification("تم النسخ", "تم نسخ الآية إلى الحافظة");
    } catch (error) {
      console.error("خطأ في نسخ الآية:", error);
      this.showNotification("خطأ", "حدث خطأ أثناء نسخ الآية");
    }
  }

  bookmarkAyah() {
    try {
      const surahNumber = this.elements.surahSelect.value;
      const ayahNumber = this.elements.ayahSelect.value;
      const surah = this.quranData[surahNumber];

      // حفظ الإشارة المرجعية
      const bookmark = {
        surah: surahNumber,
        ayah: ayahNumber,
        name: surah.name,
        timestamp: new Date().toISOString(),
      };

      // تحميل الإشارات المرجعية المحفوظة
      let bookmarks = JSON.parse(
        localStorage.getItem("quranBookmarks") || "[]"
      );

      // التحقق من عدم وجود الإشارة المرجعية مسبقاً
      const exists = bookmarks.some(
        (b) => b.surah === surahNumber && b.ayah === ayahNumber
      );

      if (!exists) {
        bookmarks.push(bookmark);
        localStorage.setItem("quranBookmarks", JSON.stringify(bookmarks));
        this.showNotification("تم الحفظ", "تمت إضافة الإشارة المرجعية بنجاح");
      } else {
        this.showNotification("تنبيه", "هذه الآية محفوظة مسبقاً");
      }
    } catch (error) {
      console.error("خطأ في حفظ الإشارة المرجعية:", error);
      this.showNotification("خطأ", "حدث خطأ أثناء حفظ الإشارة المرجعية");
    }
  }

  // محطات الراديو
  radioStations = {
    saudi: {
      name: "إذاعة القرآن الكريم - السعودية",
      url: "https://stream.radiojar.com/8s5u5tpdtwzuv",
    },
    egypt: {
      name: "إذاعة القرآن الكريم - مصر",
      url: "https://stream.radiojar.com/8s5u5tpdtwzuv",
    },
    husary: {
      name: "إذاعة الحصري",
      url: "https://Qurango.net/radio/mahmood_khalil_al_husary_murattal",
    },
    abdulbasit: {
      name: "إذاعة عبد الباسط",
      url: "https://Qurango.net/radio/abdulbasit_abdulsamad_murattal",
    },
    ajmy: {
      name: "إذاعة أحمد العجمي",
      url: "https://Qurango.net/radio/ahmed_al_ajmy",
    },
    maher: {
      name: "إذاعة ماهر المعيقلي",
      url: "https://Qurango.net/radio/maher_al_muaiqly",
    },
  };

  setupRadioSection() {
    // تهيئة عناصر الراديو
    this.audioPlayer = new Audio();
    this.audioPlayer.preload = "auto";

    // تحديث حالة الصوت
    this.audioPlayer.onplay = () => {
      this.elements.playButton.innerHTML = '<i class="fas fa-pause"></i>';
      this.elements.statusDot?.classList.add("active");
    };

    this.audioPlayer.onpause = () => {
      this.elements.playButton.innerHTML = '<i class="fas fa-play"></i>';
      this.elements.statusDot?.classList.remove("active");
    };

    // معالجة أخطاء التشغيل
    this.audioPlayer.onerror = () => {
      this.showNotification("خطأ", "حدث خطأ أثناء تشغيل المحطة");
      this.elements.statusText.textContent = "حدث خطأ في التشغيل";
      this.elements.statusDot?.classList.remove("active");
    };

    // تحديث مستوى الصوت
    this.audioPlayer.volume = 0.7;
  }

  changeStation() {
    const stationId = this.elements.stationSelect.value;
    if (!stationId) return;

    const station = this.radioStations[stationId];
    if (!station) return;

    // تحديث المصدر وتشغيل المحطة
    this.audioPlayer.src = station.url;
    this.audioPlayer.play().catch(console.error);

    // تحديث واجهة المستخدم
    this.elements.currentStation.textContent = station.name;
    this.elements.statusText.textContent = "جاري التشغيل...";
  }

  toggleRadio() {
    if (this.audioPlayer.paused) {
      this.audioPlayer.play().catch(console.error);
    } else {
      this.audioPlayer.pause();
    }
  }

  changeVolume(value) {
    const volume = parseInt(value) / 100;
    this.audioPlayer.volume = volume;
    const volumeValue = document.querySelector(".volume-value");
    if (volumeValue) volumeValue.textContent = `${value}%`;
  }

  // وظائف مواقيت الصلاة
  async loadPrayerTimes() {
    try {
      // بيانات مواقيت الصلاة للمدن المختلفة
      this.prayerTimesData = {
        cairo: {
          name: "القاهرة",
          times: {
            fajr: "04:11",
            sunrise: "05:55",
            dhuhr: "12:53",
            asr: "16:28",
            maghrib: "19:51",
            isha: "21:23",
          },
          qibla: "جنوب شرق",
        },
        mecca: {
          name: "مكة المكرمة",
          times: {
            fajr: "05:15",
            sunrise: "06:35",
            dhuhr: "12:25",
            asr: "15:45",
            maghrib: "18:15",
            isha: "19:45",
          },
          qibla: "الكعبة المشرفة",
        },
        medina: {
          name: "المدينة المنورة",
          times: {
            fajr: "05:20",
            sunrise: "06:40",
            dhuhr: "12:30",
            asr: "15:50",
            maghrib: "18:20",
            isha: "19:50",
          },
          qibla: "جنوب شرق",
        },
        riyadh: {
          name: "الرياض",
          times: {
            fajr: "05:10",
            sunrise: "06:30",
            dhuhr: "12:20",
            asr: "15:40",
            maghrib: "18:10",
            isha: "19:40",
          },
          qibla: "جنوب شرق",
        },
        alexandria: {
          name: "الإسكندرية",
          times: {
            fajr: "04:15",
            sunrise: "06:00",
            dhuhr: "12:55",
            asr: "16:30",
            maghrib: "19:55",
            isha: "21:25",
          },
          qibla: "جنوب شرق",
        },
        giza: {
          name: "الجيزة",
          times: {
            fajr: "04:12",
            sunrise: "05:56",
            dhuhr: "12:54",
            asr: "16:29",
            maghrib: "19:52",
            isha: "21:24",
          },
          qibla: "جنوب شرق",
        },
      };

      this.updatePrayerTimes();
      this.startPrayerCountdown();
    } catch (error) {
      console.error("خطأ في تحميل مواقيت الصلاة:", error);
    }
  }

  updatePrayerTimes() {
    const citySelect = document.getElementById("city-select");
    const selectedCity = citySelect?.value || "cairo";
    const cityData = this.prayerTimesData[selectedCity];

    if (!cityData) return;

    const prayerTimesContainer = this.elements.prayerTimes;
    if (!prayerTimesContainer) return;

    const prayerNames = {
      fajr: "الفجر",
      sunrise: "الشروق",
      dhuhr: "الظهر",
      asr: "العصر",
      maghrib: "المغرب",
      isha: "العشاء",
    };

    let html = "";
    Object.entries(cityData.times).forEach(([prayer, time]) => {
      html += `
                <div class="prayer-time-item">
                    <span class="prayer-name">${prayerNames[prayer]}</span>
                    <span class="prayer-time">${time}</span>
                </div>
            `;
    });

    prayerTimesContainer.innerHTML = html;
  }

  updateCurrentDate() {
    const currentDateElement = document.getElementById("current-date");
    if (!currentDateElement) return;

    const now = new Date();
    const hijriDate = this.getHijriDate(now);
    const gregorianDate = now.toLocaleDateString("ar-EG", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    currentDateElement.innerHTML = `
            <div class="date-gregorian">${gregorianDate}</div>
            <div class="date-hijri">${hijriDate}</div>
        `;
  }

  getHijriDate(date) {
    // تاريخ هجري تقريبي - يمكن تحسينه باستخدام مكتبة متخصصة
    return "7 ذو الحجة 1446 هـ";
  }

  startPrayerCountdown() {
    setInterval(() => {
      this.updatePrayerCountdown();
    }, 1000);
  }

  updatePrayerCountdown() {
    const citySelect = document.getElementById("city-select");
    const selectedCity = citySelect?.value || "cairo";
    const cityData = this.prayerTimesData[selectedCity];

    if (!cityData) return;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    // تحويل أوقات الصلاة إلى دقائق
    const prayerTimes = {};
    Object.entries(cityData.times).forEach(([prayer, time]) => {
      const [hours, minutes] = time.split(":").map(Number);
      prayerTimes[prayer] = hours * 60 + minutes;
    });

    // العثور على الصلاة القادمة
    let nextPrayer = null;
    let nextTime = null;
    let minDiff = Infinity;

    Object.entries(prayerTimes).forEach(([prayer, time]) => {
      let diff = time - currentTime;
      if (diff < 0) diff += 24 * 60; // إضافة يوم كامل

      if (diff < minDiff) {
        minDiff = diff;
        nextPrayer = prayer;
        nextTime = time;
      }
    });

    if (nextPrayer) {
      const prayerNames = {
        fajr: "الفجر",
        sunrise: "الشروق",
        dhuhr: "الظهر",
        asr: "العصر",
        maghrib: "المغرب",
        isha: "العشاء",
      };

      const hours = Math.floor(minDiff / 60);
      const minutes = minDiff % 60;

      const nextPrayerNameElement = this.elements.nextPrayerName;
      const countdownTimerElement = this.elements.countdownTimer;

      if (nextPrayerNameElement) {
        nextPrayerNameElement.textContent = prayerNames[nextPrayer];
      }
      if (countdownTimerElement) {
        countdownTimerElement.textContent = `${hours
          .toString()
          .padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
      }
    }
  }

  // وظائف الأذكار
  setupAdhkarSection() {
    try {
      this.displayAdhkar("all");
      this.setupAdhkarFilters();
    } catch (error) {
      console.error("خطأ في إعداد قسم الأذكار:", error);
    }
  }

  setupAdhkarFilters() {
    const filterButtons = document.querySelectorAll(".filter-btn");
    filterButtons.forEach((button) => {
      button.addEventListener("click", () => {
        // إزالة الفئة النشطة من جميع الأزرار
        filterButtons.forEach((btn) => btn.classList.remove("active"));
        // إضافة الفئة النشطة للزر المضغوط
        button.classList.add("active");
        // عرض الأذكار المفلترة
        this.displayAdhkar(button.dataset.filter);
      });
    });
  }

  displayAdhkar(filter) {
    const adhkarList = document.getElementById("adhkar-list");
    if (!adhkarList || !window.adhkarData) return;

    let adhkarToShow = [];

    if (filter === "all") {
      // عرض جميع الأذكار
      Object.values(window.adhkarData).forEach((category) => {
        adhkarToShow = adhkarToShow.concat(category);
      });
    } else {
      // عرض فئة محددة
      adhkarToShow = window.adhkarData[filter] || [];
    }

    let html = "";
    adhkarToShow.forEach((dhikr, index) => {
      html += `
                <div class="dhikr-item">
                    <div class="dhikr-text">${dhikr.text}</div>
                    <div class="dhikr-meta">
                        <span class="dhikr-count">العدد: ${dhikr.count}</span>
                        <span class="dhikr-reference">${dhikr.reference}</span>
                    </div>
                </div>
            `;
    });

    adhkarList.innerHTML = html;
  }

  // وظائف الصوت والحفظ
  async loadSounds() {
    try {
      this.sounds = {
        click: new Audio("./sounds/click.mp3"),
        complete: new Audio("./sounds/complete.mp3"),
        milestone: new Audio("./sounds/milestone.mp3"),
        adhan1: new Audio("./sounds/الاذان 1.mp3"),
        adhan2: new Audio("./sounds/الاذان 2.mp3"),
      };

      // تحديد مستوى الصوت
      Object.values(this.sounds).forEach((sound) => {
        sound.volume = this.state.soundVolume || 0.7;
        sound.preload = "auto";
      });

      // تحميل الأصوات مسبقاً
      await Promise.all(
        Object.values(this.sounds).map((sound) => {
          return new Promise((resolve) => {
            sound.addEventListener("canplaythrough", resolve, { once: true });
            sound.load();
          });
        })
      );

      console.log("✅ تم تحميل جميع الأصوات بنجاح");
    } catch (error) {
      console.error("خطأ في تحميل الأصوات:", error);
    }
  }

  playSound(soundName) {
    if (this.state.soundEnabled && this.sounds[soundName]) {
      try {
        // إعادة تعيين الصوت للبداية
        this.sounds[soundName].currentTime = 0;

        // تحديث مستوى الصوت
        this.sounds[soundName].volume = this.state.soundVolume || 0.7;

        // تشغيل الصوت
        const playPromise = this.sounds[soundName].play();

        if (playPromise !== undefined) {
          playPromise.catch((error) => {
            console.warn("تعذر تشغيل الصوت:", error);
          });
        }

        // إضافة تأثير بصري عند تشغيل الصوت
        this.showSoundEffect(soundName);
      } catch (error) {
        console.error("خطأ في تشغيل الصوت:", error);
      }
    }
  }

  showSoundEffect(soundName) {
    // إضافة تأثير بصري للصوت
    const button = document.getElementById("tasbih-button");
    if (button && soundName === "click") {
      button.classList.add("sound-playing");
      setTimeout(() => {
        button.classList.remove("sound-playing");
      }, 200);
    }
  }

  loadSavedState() {
    try {
      // تحميل الحالة من التخزين المحلي
      const rawState = localStorage.getItem("tasbihAppState");
      const savedState = rawState ? JSON.parse(rawState) : null;

      if (savedState) {
        this.state = { ...this.state, ...savedState };
      }
    } catch (error) {
      console.error("خطأ في تحميل الحالة المحفوظة:", error);
    }
  }

  saveState() {
    try {
      // حفظ الحالة في التخزين المحلي
      localStorage.setItem("tasbihAppState", JSON.stringify(this.state));
    } catch (error) {
      console.error("خطأ في حفظ الحالة:", error);
    }
  }

  showNotification(title, message) {
    // يمكن تحسين هذه الوظيفة لاحقاً
    console.log(`${title}: ${message}`);
  }

  applyTheme() {
    document.body.className = this.state.theme;
  }
}
